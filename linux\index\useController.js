import { ref, reactive, watch, computed } from 'vue';
import { getServerList, getServerDetail as getServerDetailApi } from '@/api/serverList';
import { useConfigStore } from '@/store/modules/config';
import throttle from '@/uni_modules/uv-ui-tools/libs/function/throttle.js';
import { getChartColorStops } from '@/pages/index/serverList/useController';
import { $t } from '@/locale/index.js';
import bgLight from '@/static/index/bg-light.png';

// ==================== 网络数据状态 ====================
// 网络流量数据
export const upFlow = ref('0');
export const downFlow = ref('0');
export const upTotal = ref('0');
export const downTotal = ref('0');
export const networkData = reactive({
	xData: [], // 时间
	upData: [], // 上行数据
	downData: [], // 下行数据
});

// 磁盘IO数据
export const readBytes = ref('0');
export const writeBytes = ref('0');
export const ioCount = ref(0);
export const ioDelay = ref(0);
export const ioDelayColor = ref('#20a53a');
export const diskIOData = reactive({
	xData: [], // 时间
	readData: [], // 读取数据
	writeData: [], // 写入数据
});

// 图表数据 - 适配ECharts组件格式
export const networkChartData = reactive({
	categories: [], // x轴数据
	series: [
		{
			name: $t('linux.network.up'),
			type: 'line',
			smooth: true,
			showSymbol: false,
			symbolSize: 8,
			lineStyle: {
				width: 3,
			},
			data: [],
		},
		{
			name: $t('linux.network.down'),
			type: 'line',
			smooth: true,
			showSymbol: false,
			symbolSize: 8,
			lineStyle: {
				width: 3,
			},
			data: [],
		},
	],
	color: ['#ff8c00', '#1e90ff'],
});

// 磁盘IO图表数据 - 适配ECharts组件格式
export const diskIOChartData = reactive({
	categories: [], // x轴数据
	series: [
		{
			name: $t('linux.io.read'),
			type: 'line',
			smooth: true,
			showSymbol: false,
			symbolSize: 8,
			lineStyle: {
				width: 3,
			},
			data: [],
		},
		{
			name: $t('linux.io.write'),
			type: 'line',
			smooth: true,
			showSymbol: false,
			symbolSize: 8,
			lineStyle: {
				width: 3,
			},
			data: [],
		},
	],
	color: ['#FF4683', '#6CC0CF'],
});

// 定时器ID
let timer = null;

// ==================== 网络相关方法 ====================
export const networkInfo = ref({});

// 版本比较函数
export const compareVersion = (version1, version2) => {
	if (!version1 || !version2) return false;

	const v1Parts = version1.split('.').map(Number);
	const v2Parts = version2.split('.').map(Number);

	// 补齐版本号位数
	const maxLength = Math.max(v1Parts.length, v2Parts.length);
	while (v1Parts.length < maxLength) v1Parts.push(0);
	while (v2Parts.length < maxLength) v2Parts.push(0);

	// 逐位比较
	for (let i = 0; i < maxLength; i++) {
		if (v1Parts[i] > v2Parts[i]) return 1;
		if (v1Parts[i] < v2Parts[i]) return -1;
	}
	return 0;
};

// 检查是否支持节点管理功能
export const isNodeManagementSupported = ref(false);

// 页面容器引用
export const pageContainer = ref(null);

// ==================== 服务器信息显示相关 ====================
// 服务器基础信息
export const serverDisplayInfo = computed(() => {
	const { currentServerInfo } = useConfigStore().getReactiveState();

	if (!networkInfo.value) {
		return {
			name: currentServerInfo.value?.name || '宝塔面板',
			ip: currentServerInfo.value?.ip || '--',
			system: '--',
			uptime: '--',
			isOnline: false,
		};
	}

	return {
		name: networkInfo.value.title || currentServerInfo.value?.name || '宝塔面板',
		ip: currentServerInfo.value?.ip || networkInfo.value.ip || '--',
		system: networkInfo.value.simple_system || networkInfo.value.system || '--',
		uptime: `持续运行${networkInfo.value.time}` || '--',
		isOnline: networkInfo.value.status !== false,
	};
});

// 服务器状态指示器颜色
export const serverStatusColor = computed(() => {
	if (!networkInfo.value || networkInfo.value.status === false) {
		return '#E7E7E7'; // 离线状态为灰色
	}
	return '#20a50a'; // 在线状态为绿色
});

export const getNetwork = async () => {
	const { panelVersion } = useConfigStore().getReactiveState();
	try {
		const res = await getServerList();
		networkInfo.value = res;
		panelVersion.value = res.version;

		// 检查版本是否支持节点管理功能（需要 >= 9.7.0）
		isNodeManagementSupported.value = compareVersion(res.version, '9.7.0') >= 0;

		await getServerDetail(res);
		await getServerStatistics(res);
	} catch (error) {
		console.log(error);
	}
};

// 获取服务器统计
export const websiteTotal = ref(0);
export const safetyTotal = ref(0);
export const databaseTotal = ref(0);
export const getServerStatistics = async (info) => {
	try {
		websiteTotal.value = info.site_total;
		databaseTotal.value = info.database_total;
		safetyTotal.value = await getServerDetailApi();
	} catch (error) {}
};

// 服务器磁盘列表
export const diskList = ref([]);
// 获取服务器详情的值
export const chartMap = ref({});
export const getServerDetail = async (info) => {
	try {
		// 负载
		let loadCount =
			Math.round((info.load.one / info.load.max) * 100) > 100
				? 100
				: Math.round((info.load.one / info.load.max) * 100);
		loadCount = loadCount < 0 ? 0 : loadCount;
		chartMap.value['load'] = handleServerInfo(loadCount, 'load');
		// cpu
		let cpuCount = info.cpu[0];
		chartMap.value['cpu'] = handleServerInfo(cpuCount, 'cpu');
		// 内存
		const memCount = Math.round((info.mem.memRealUsed / info.mem.memTotal) * 1000) / 10; // 返回 memRealUsed 占 memTotal 的百分比
		chartMap.value['mem'] = handleServerInfo(memCount, 'mem');
		diskList.value = info.disk;
		let diskJson = [];
		for (let i = 0; i < diskList.value.length; i++) {
			let ratio = diskList.value[i].size[3];
			ratio = parseFloat(ratio.substring(0, ratio.lastIndexOf('%')));
			let diskInfo = handleDiskInfo(ratio, i);
			diskJson.push(diskInfo);
		}
		chartMap.value['disk'] = diskJson;
		updateChartData();
		handlePicker(info);
	} catch (error) {
		console.log(error);
	}
};

// 处理磁盘信息
export const handleDiskInfo = (ratio, index) => {
	try {
		let diskInfo = {};
		// 记录实际负载值
		diskInfo.val = ratio;

		diskInfo.path = networkInfo.value.disk[index].path;

		diskInfo.title = networkInfo.value.disk[index].size[0];

		return diskInfo;
	} catch (error) {}
};
// 处理对应的服务器信息
export const handleServerInfo = (number, name) => {
	try {
		// 根据负载区间判断状态
		const loadList = [
			{ val: 90, title: $t('linux.blocked'), color: '#dd2f00' },
			{ val: 80, title: $t('linux.slow'), color: '#ff9900' },
			{ val: 70, title: $t('linux.normal'), color: '#20a53a' },
			{ val: 30, title: $t('linux.smooth'), color: '#20a53a' },
		];

		let activeInfo = {};

		// 从高到低匹配第一个符合条件的负载区间
		for (let i = 0; i < loadList.length; i++) {
			if (number >= loadList[i].val) {
				activeInfo = { ...loadList[i] };
				break;
			} else if (number <= 30) {
				activeInfo = { ...loadList[3] };
				break;
			}
		}

		// 记录实际负载值
		activeInfo.val = number;

		switch (name) {
			case 'load':
				activeInfo.title = activeInfo.title;
				break;
			case 'cpu':
				activeInfo.title = `${networkInfo.value.cpu[1]} ${$t('linux.cores')}`;
				break;
			case 'mem':
				activeInfo.title = `${Math.round(networkInfo.value.mem.memTotal / 1024)} G`;
				break;
		}

		return activeInfo;
	} catch (error) {
		console.error('处理服务器信息出错:', error);
		return null;
	}
};

// 根据IO延迟设置颜色
export const updateIODelayColor = (delay) => {
	if (delay > 100 && delay < 1000) {
		ioDelayColor.value = '#ff9900';
	} else if (delay >= 1000) {
		ioDelayColor.value = '#dd2f00';
	} else {
		ioDelayColor.value = '#20a53a';
	}
};

// 格式化字节大小
export const formatBytes = (bytes) => {
	if (bytes === 0) return '0 B';
	const k = 1024;
	const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取当前时间格式化字符串
export const getCurrentTime = () => {
	const now = new Date();
	const hours = now.getHours().toString().padStart(2, '0');
	const minutes = now.getMinutes().toString().padStart(2, '0');
	const seconds = now.getSeconds().toString().padStart(2, '0');
	return `${hours}:${minutes}:${seconds}`;
};

// 更新图表数据
export const updateChartData = () => {
	// 模拟获取网络和磁盘IO数据
	const currentTime = getCurrentTime();
	if (networkValue.value === 'ALL') {
		upFlow.value = networkInfo.value.up;
		downFlow.value = networkInfo.value.down;
	} else {
		upFlow.value = networkInfo.value.network[networkValue.value].up;
		downFlow.value = networkInfo.value.network[networkValue.value].down;
	}

	// 添加新数据点，并限制为5个数据点
	if (networkData.xData.length >= 5) {
		networkData.xData.shift();
		networkData.upData.shift();
		networkData.downData.shift();
	}
	networkData.xData.push(currentTime);
	networkData.upData.push(upFlow.value);
	networkData.downData.push(downFlow.value);

	// 更新网络图表数据 - 使用深拷贝触发视图更新
	networkChartData.categories = [...networkData.xData];
	networkChartData.series[0].data = [...networkData.upData];
	networkChartData.series[1].data = [...networkData.downData];

	// 磁盘IO数据
	let ioData = networkInfo.value.iostat[ioValue.value];
	readBytes.value = ioData.read_bytes;
	writeBytes.value = ioData.write_bytes;

	// ioCount.value = Math.floor(Math.random() * 100);
	// ioDelay.value = Math.floor(Math.random() * 200);
	// updateIODelayColor(ioDelay.value);

	// 添加新数据点，并限制为5个数据点
	if (diskIOData.xData.length >= 5) {
		diskIOData.xData.shift();
		diskIOData.readData.shift();
		diskIOData.writeData.shift();
	}
	diskIOData.xData.push(currentTime);
	diskIOData.readData.push(readBytes.value / 1024 / 1024).toFixed(2);
	diskIOData.writeData.push(writeBytes.value / 1024 / 1024).toFixed(2);

	// 更新磁盘IO图表数据 - 使用深拷贝触发视图更新
	diskIOChartData.categories = [...diskIOData.xData];
	diskIOChartData.series[0].data = [...diskIOData.readData];
	diskIOChartData.series[1].data = [...diskIOData.writeData];

	// 强制更新图表配置，确保x轴数据刷新
	chartOpts.xAxis.data = [...networkChartData.categories];
};

// 根据类型初始化图表数据格式
export const initChartByType = (type) => {
	if (type === 0) {
		// 网络图表
		networkChartData.series.forEach((item) => {
			item.type = 'line';
		});
	} else {
		// 磁盘IO图表
		diskIOChartData.series.forEach((item) => {
			item.type = 'line';
		});
	}
};

// 生成初始数据
export const initData = () => {
	// 重置数据数组，确保每次调用都是全新的数据
	networkData.xData = [];
	networkData.upData = [];
	networkData.downData = [];
	diskIOData.xData = [];
	diskIOData.readData = [];
	diskIOData.writeData = [];
	networkList.value = [];
	ioList.value = [];
	networkValue.value = 'ALL';
	ioValue.value = 'ALL';
	networkName.value = $t('linux.all');
	ioName.value = $t('linux.all');
	subsecType.value = 0;
};

// 启动定时器
export const startTimer = () => {
	// 确保先停止任何可能存在的定时器
	stopTimer();

	// 设置定时器，每3秒更新一次数据
	timer = setInterval(getNetwork, 3000);
};

// 停止定时器
export const stopTimer = () => {
	// 清除定时器
	if (timer) {
		clearInterval(timer);
		timer = null;
	}
};

// ==================== 图表选项 ====================
// 图表基础配置
export const chartOpts = reactive({
	grid: {
		bottom: 30,
		right: 20,
	},
	xAxis: {
		axisLine: {},
		axisLabel: {
			fontSize: 10,
			margin: 10,
			rotate: 0,
		},
		type: 'category',
		data: [],
	},
	yAxis: {
		scale: true, // 启用y轴自动缩放
		nameTextStyle: {
			padding: [0, 0, 0, 0],
			color: '#999999',
			fontSize: 10,
		},
		axisLabel: {
			color: '#999999',
			fontSize: 10,
			margin: 10,
		},
		splitLine: {
			lineStyle: {
				color: '#999999',
				type: 'dashed', // 设置为虚线类型
			},
		},
	},
	tooltip: {
		trigger: 'axis',
		axisPointer: {
			type: 'cross',
		},
	},
	legend: {
		top: 5,
		left: 'center',
		textStyle: {
			color: '#999999',
			fontSize: 10,
		},
		itemWidth: 15,
		itemHeight: 10,
		itemGap: 20,
	},
	title: {
		left: 'center',
		top: 10,
		textStyle: {
			fontSize: 10,
		},
	},
});

// 网络图表专属配置
export const getNetworkChartOpts = () => {
	return {
		...chartOpts,
		xAxis: {
			...chartOpts.xAxis,
			data: networkChartData.categories,
		},
		yAxis: {
			...chartOpts.yAxis,
			name: $t('linux.unitKBS'),
			axisLabel: {
				...chartOpts.yAxis.axisLabel,
				formatter: (value) => `${value} KB/s`,
			},
		},
		title: {
			...chartOpts.title,
			text: ' ',
		},
	};
};

// IO图表专属配置
export const getDiskIOChartOpts = () => {
	return {
		...chartOpts,
		xAxis: {
			...chartOpts.xAxis,
			data: diskIOChartData.categories,
		},
		yAxis: {
			...chartOpts.yAxis,
			name: $t('linux.unitMBS'),
			axisLabel: {
				...chartOpts.yAxis.axisLabel,
				formatter: (value) => `${value} MB/s`,
			},
		},
		title: {
			...chartOpts.title,
			text: ' ',
		},
	};
};

// ==================== 区域控制 ====================
export const picker = ref(null);
export const subsecType = ref(0); // 0: 网络, 1: 磁盘IO
export const networkList = ref([]);
export const networkName = ref($t('linux.all'));
export const networkValue = ref('ALL');
export const ioList = ref([]);
export const ioName = ref($t('linux.all'));
export const ioValue = ref('ALL');
export const defaultIndex = ref([0]);

export const openPicker = () => {
	let index = -1;

	if (subsecType.value === 0) {
		index = networkList.value.findIndex((item) => item.value === networkValue.value);
	} else {
		index = ioList.value.findIndex((item) => item.value === ioValue.value);
	}

	// 确保找到有效索引，否则默认为第一项
	defaultIndex.value = [index >= 0 ? index : 0];

	// 确保picker存在再调用open方法
	picker.value?.open();
};

const handlePicker = (res) => {
	// 网卡
	if (networkList.value.length == 0 && ioList.value.length == 0) {
		for (let key in res.iostat) {
			let ioItem = {
				title: key == 'ALL' ? $t('linux.all') : key,
				value: key,
			};
			ioList.value.push(ioItem);
		}

		networkList.value.push({
			title: $t('linux.all'),
			value: 'ALL',
		});

		for (let key in res.network) {
			let netItem = {
				title: key,
				value: key,
			};
			networkList.value.push(netItem);
		}
	}
};

export const confirm = (e) => {
	if (subsecType.value === 0) {
		networkValue.value = e.value[0].value;
		networkName.value = e.value[0].title;
	} else {
		ioValue.value = e.value[0].value;
		ioName.value = e.value[0].title;
	}
};

// 当subsection切换时
export const onSectionChange = (index) => {
	subsecType.value = index;
	if (index === 0) {
		networkValue.value = 'ALL';
		networkName.value = $t('linux.all');
	} else {
		ioValue.value = 'ALL';
		ioName.value = $t('linux.all');
	}
};

export const handleModuleAction = (type) => {
	switch (type) {
		case 'website':
			uni.navigateTo({
				url: '/linux/website/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'database':
			uni.navigateTo({
				url: '/linux/database/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'file':
			uni.navigateTo({
				url: '/linux/files/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'security':
			uni.navigateTo({
				url: '/linux/firewall/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'terminal':
			uni.navigateTo({
				url: '/pages/ssh/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'control':
			uni.navigateTo({
				url: '/linux/control/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'ssh':
			uni.navigateTo({
				url: '/linux/ssh/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'setting':
			uni.navigateTo({
				url: '/linux/setting/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'monitorReport':
			uni.navigateTo({
				url: '/linux/monitorReport/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'nginx':
			uni.navigateTo({
				url: '/linux/nginx/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'node':
			uni.navigateTo({
				url: '/linux/node/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'nginxEnv':
			uni.navigateTo({
				url: '/linux/nginxEnv/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'crontab':
			uni.navigateTo({
				url: '/linux/crontab/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'mysql':
			uni.navigateTo({
				url: '/linux/mysql/index',
				animationType: 'zoom-fade-out',
			});
			break;
	}
};

// ==================== 功能列表点击事件处理 ====================

/**
 * 处理基础功能列表项点击事件
 * @param {Object} event - 点击事件对象，包含 item 和 index
 */
export const handleBasicFunctionClick = (event) => {
	const { item } = event;
	const label = item.label;
	switch (label) {
		case '网站':
			handleModuleAction('website');
			break;
		case '数据库':
			handleModuleAction('database');
			break;
		case '系统防火墙':
			handleModuleAction('security');
			break;
		case '终端':
			handleModuleAction('terminal');
			break;
		case '安全风险':
			// TODO: 添加安全风险页面路由
			pageContainer.value?.notify?.primary('安全风险功能开发中...');
			break;
		case '监控':
			handleModuleAction('control');
			break;
		case 'SSH管理':
			handleModuleAction('ssh');
			break;
		case '文件管理':
			handleModuleAction('file');
			break;
		case '计划任务':
			handleModuleAction('crontab');
			break;
		case '日志':
			// TODO: 添加日志页面路由
			pageContainer.value?.notify?.primary('日志功能开发中...');
			break;
		default:
			pageContainer.value?.notify?.warning('未知功能类型');
	}
};

/**
 * 处理插件功能列表项点击事件
 * @param {Object} event - 点击事件对象，包含 item 和 index
 */
export const handlePluginFunctionClick = (event) => {
	const { item } = event;
	const label = item.label;

	switch (label) {
		case '站点监控':
			handleModuleAction('monitorReport');
			break;
		case '系统加固':
			// TODO: 添加系统加固页面路由
			pageContainer.value?.notify?.primary('系统加固功能开发中...');
			break;
		case 'nginx防火墙':
			handleModuleAction('nginx');
			break;
		case '防篡改':
			// TODO: 添加防篡改页面路由
			pageContainer.value?.notify?.primary('防篡改功能开发中...');
			break;
		default:
			pageContainer.value?.notify?.warning('未知插件类型');
	}
};

/**
 * 处理环境功能列表项点击事件
 * @param {Object} event - 点击事件对象，包含 item 和 index
 */
export const handleEnvironmentFunctionClick = (event) => {
	const { item } = event;
	const label = item.label;

	switch (label) {
		case 'nginx':
			handleModuleAction('nginxEnv');
			break;
		case 'mysql':
			handleModuleAction('mysql');
			break;
		case 'redis':
			// TODO: 添加 Redis 环境管理页面路由
			pageContainer.value?.notify?.primary('Redis 环境管理功能开发中...');
			break;
		default:
			pageContainer.value?.notify?.warning('未知环境类型');
	}
};

/**
 * 处理功能列表编辑按钮点击事件
 * @param {String} listType - 列表类型：'basic', 'plugin', 'environment'
 */
export const handleFunctionListEdit = (listType) => {
	switch (listType) {
		case 'basic':
			pageContainer.value?.notify?.primary('基础功能编辑功能开发中...');
			break;
		case 'plugin':
			pageContainer.value?.notify?.primary('插件功能编辑功能开发中...');
			break;
		case 'environment':
			pageContainer.value?.notify?.primary('环境功能编辑功能开发中...');
			break;
		default:
			pageContainer.value?.notify?.warning('未知编辑类型');
	}
};

// ==================== 监听数据变化 ====================
// 监听网络和磁盘IO数据变化
watch(
	() => ([...networkChartData.categories], [...diskIOChartData.categories]),
	() => {
		// 确保图表配置的xAxis数据也同步更新
		if (subsecType.value === 0) {
			// 网络图表当前激活
			chartOpts.xAxis.data = [...networkChartData.categories];
		} else {
			// 磁盘IO图表当前激活
			chartOpts.xAxis.data = [...diskIOChartData.categories];
		}
	},
	{ deep: true },
);

// 监听子系统类型变化，切换对应的x轴数据
watch(
	() => subsecType.value,
	(newType) => {
		if (newType === 0) {
			// 切换到网络图表
			chartOpts.xAxis.data = [...networkChartData.categories];
		} else {
			// 切换到磁盘IO图表
			chartOpts.xAxis.data = [...diskIOChartData.categories];
		}
	},
);

// 获取磁盘列表名称
export const getDiskListName = (path) => {
	if (path === '/') {
		return '/';
	}
	const parts = path.split('/').filter((part) => part.length > 0);
	if (parts.length > 0) {
		return '/' + parts[parts.length - 1];
	}
	// 如果路径无效或为空，也返回 '/'
	return '/';
};

// 获取基础图表配置
export const getBaseChartConfig = (usage, label) => {
	// 确保usage是数字
	usage = parseFloat(usage) || 0;

	// 创建颜色渐变配置
	const colorStops = getChartColorStops(usage);

	// 创建仪表盘数据
	const gaugeData = [
		{
			value: usage,
			name: label,
			title: {
				show: false,
			},
			detail: {
				valueAnimation: true,
				offsetCenter: [0, '0%'],
				fontSize: 10,
				color: '#A7A7A7',
				formatter: '{value}%',
			},
			itemStyle: {
				color: {
					type: 'linear',
					x: 0,
					y: 0,
					x2: 0,
					y2: 1,
					colorStops: [...colorStops], // 使用深拷贝
				},
			},
		},
	];

	// 返回图表配置
	return {
		series: [
			{
				type: 'gauge',
				radius: '100%',
				center: ['65%', '65%'],
				startAngle: 180,
				endAngle: 0,
				pointer: {
					show: false,
				},
				progress: {
					show: true,
					overlap: false,
					roundCap: true,
					clip: false,
					itemStyle: {
						borderWidth: 6,
					},
				},
				axisLine: {
					lineStyle: {
						width: 8,
					},
				},
				axisTick: {
					show: false,
				},
				splitLine: {
					show: false,
				},
				axisLabel: {
					show: false,
					fontSize: 9,
				},
				title: {
					show: false,
				},
				data: gaugeData,
				detail: {
					valueAnimation: true,
					fontSize: 10,
					color: '#A7A7A7',
					formatter: '{value}%',
					borderRadius: 20,
				},
				animation: true,
				animationDuration: 1500,
				animationDurationUpdate: 1000,
				animationEasing: 'cubicInOut',
			},
		],
	};
};

// ==================== 图表配置重构 ====================
// 公共图表配置 - 定义通用的样式和行为
const commonChartConfig = {
	series: {
		type: 'line',
		smooth: true,
		symbol: 'none',
		lineStyle: {
			width: 2,
		},
		areaStyle: {
			color: {
				type: 'linear',
				x: 0,
				y: 0,
				x2: 0,
				y2: 1,
				colorStops: [
					{
						offset: 0,
						color: '', // 将在工厂函数中动态设置
					},
					{
						offset: 1,
						color: '', // 将在工厂函数中动态设置
					},
				],
			},
		},
	},
	// 默认数据
	defaultData: [820, 932, 901, 934, 1290, 1330, 1320, 1320, 1320, 1320, 1883, 0, 123, 456, 199],
};

// 图表颜色配置映射
const chartColorConfig = {
	load: {
		primary: 'rgba(170, 255, 128, 1)',
		topGradient: 'rgba(170, 255, 128, 0.8)',
		bottomGradient: 'rgba(170, 255, 128, 0.1)',
	},
	cpu: {
		primary: 'rgba(170, 255, 128, 1)',
		topGradient: 'rgba(170, 255, 128, 0.8)',
		bottomGradient: 'rgba(170, 255, 128, 0.1)',
	},
	memory: {
		primary: 'rgba(170, 255, 128, 1)',
		topGradient: 'rgba(170, 255, 128, 0.8)',
		bottomGradient: 'rgba(170, 255, 128, 0.1)',
	},
	// 双面积图颜色配置
	network: {
		// 第一个系列（上传）
		primary: 'rgba(170, 255, 128, 1)',
		topGradient: 'rgba(170, 255, 128, 0.8)',
		bottomGradient: 'rgba(170, 255, 128, 0.1)',
		// 第二个系列（下载）
		secondary: 'rgba(255, 140, 0, 1)', // #1e90ff
		secondaryTopGradient: 'rgba(255, 140, 0, 0.8)',
		secondaryBottomGradient: 'rgba(255, 140, 0, 0.1)',
	},
	diskio: {
		// 第一个系列（读取）
		primary: 'rgba(255, 70, 131, 1)', // #FF4683
		topGradient: 'rgba(255, 70, 131, 0.8)',
		bottomGradient: 'rgba(255, 70, 131, 0.1)',
		// 第二个系列（写入）
		secondary: 'rgba(108, 192, 207, 1)', // #6CC0CF
		secondaryTopGradient: 'rgba(108, 192, 207, 0.8)',
		secondaryBottomGradient: 'rgba(108, 192, 207, 0.1)',
	},
	// 自定义双面积图默认配置
	custom: {
		// 第一个系列
		primary: 'rgba(32, 165, 10, 1)', // 主题绿色
		topGradient: 'rgba(32, 165, 10, 0.8)',
		bottomGradient: 'rgba(32, 165, 10, 0.1)',
		// 第二个系列
		secondary: 'rgba(255, 193, 7, 1)', // 黄色
		secondaryTopGradient: 'rgba(255, 193, 7, 0.8)',
		secondaryBottomGradient: 'rgba(255, 193, 7, 0.1)',
	},
};

/**
 * 创建图表数据配置的工厂函数（支持单系列和双系列）
 * @param {string} type - 图表类型 ('load', 'cpu', 'memory', 'network', 'diskio', 'custom')
 * @param {Array|Array[]} data - 图表数据，可选，默认使用通用数据。双系列时传入二维数组 [[data1], [data2]]
 * @param {Object} customConfig - 自定义配置，可选
 * @param {Object} seriesConfig - 系列配置，可选。格式：{ seriesNames: ['系列1', '系列2'], isDoubleArea: true }
 * @returns {Object} 响应式图表配置对象
 */
export const createChartData = (type, data = null, customConfig = {}, seriesConfig = {}) => {
	// 获取对应类型的颜色配置
	const colorConfig = chartColorConfig[type] || chartColorConfig.load;

	// 智能检测是否为双系列图表
	const isDoubleArea =
		seriesConfig.isDoubleArea ||
		(Array.isArray(data) && Array.isArray(data[0])) ||
		['network', 'diskio'].includes(type);

	if (isDoubleArea) {
		// 创建双系列图表
		return createDoubleSeriesChart(type, data, customConfig, seriesConfig, colorConfig);
	} else {
		// 创建单系列图表（保持向后兼容）
		return createSingleSeriesChart(type, data, customConfig, colorConfig);
	}
};

/**
 * 创建单系列图表的内部函数
 */
const createSingleSeriesChart = (type, data, customConfig, colorConfig) => {
	// 使用传入的数据或默认数据
	const chartData = data || commonChartConfig.defaultData;

	// 创建基础配置
	const baseConfig = {
		series: [
			{
				...commonChartConfig.series,
				data: chartData,
				lineStyle: {
					...commonChartConfig.series.lineStyle,
					color: colorConfig.primary,
				},
				areaStyle: {
					...commonChartConfig.series.areaStyle,
					color: {
						...commonChartConfig.series.areaStyle.color,
						colorStops: [
							{
								offset: 0,
								color: colorConfig.topGradient,
							},
							{
								offset: 1,
								color: colorConfig.bottomGradient,
							},
						],
					},
				},
				// 合并自定义series配置
				...customConfig.series,
			},
		],
		color: [colorConfig.primary],
		// 合并其他自定义配置
		...customConfig,
	};

	return reactive(baseConfig);
};

/**
 * 创建双系列图表的内部函数
 */
const createDoubleSeriesChart = (type, data, customConfig, seriesConfig, colorConfig) => {
	// 获取系列名称
	const defaultSeriesNames = getDefaultSeriesNames(type);
	const seriesNames = seriesConfig.seriesNames || defaultSeriesNames;

	// 处理数据
	let seriesData = [];
	if (Array.isArray(data) && Array.isArray(data[0])) {
		// 二维数组数据
		seriesData = data;
	} else if (Array.isArray(data)) {
		// 一维数组，复制为两个系列
		seriesData = [data, [...data]];
	} else {
		// 使用默认数据
		seriesData = [commonChartConfig.defaultData, [...commonChartConfig.defaultData]];
	}

	// 创建双系列配置
	const baseConfig = {
		categories: [], // x轴数据
		series: [
			// 第一个系列
			{
				name: seriesNames[0],
				...commonChartConfig.series,
				data: seriesData[0],
				lineStyle: {
					...commonChartConfig.series.lineStyle,
					color: colorConfig.primary,
				},
				areaStyle: {
					...commonChartConfig.series.areaStyle,
					color: {
						...commonChartConfig.series.areaStyle.color,
						colorStops: [
							{
								offset: 0,
								color: colorConfig.topGradient,
							},
							{
								offset: 1,
								color: colorConfig.bottomGradient,
							},
						],
					},
				},
			},
			// 第二个系列
			{
				name: seriesNames[1],
				...commonChartConfig.series,
				data: seriesData[1],
				lineStyle: {
					...commonChartConfig.series.lineStyle,
					color: colorConfig.secondary,
				},
				areaStyle: {
					...commonChartConfig.series.areaStyle,
					color: {
						...commonChartConfig.series.areaStyle.color,
						colorStops: [
							{
								offset: 0,
								color: colorConfig.secondaryTopGradient,
							},
							{
								offset: 1,
								color: colorConfig.secondaryBottomGradient,
							},
						],
					},
				},
			},
		],
		color: [colorConfig.primary, colorConfig.secondary],
		// 合并其他自定义配置
		...customConfig,
	};

	return reactive(baseConfig);
};

/**
 * 获取默认系列名称
 */
const getDefaultSeriesNames = (type) => {
	const defaultNames = {
		network: ['上传', '下载'],
		diskio: ['读取', '写入'],
		custom: ['系列1', '系列2'],
	};
	return defaultNames[type] || ['系列1', '系列2'];
};

// 重构后的负载图表样式配置函数
export const getLoadChartStyle = (loadData) => {
	return {
		...chartOpts,
		yAxis: {
			...chartOpts.yAxis,
			name: '负载',
			axisLine: { show: false },
			axisTick: { show: false },
			axisLabel: { show: false },
			splitLine: {
				show: true, // 保留横向辅助线
				lineStyle: {
					type: 'dashed', // 设置为虚线类型
					color: '#E7E7E7',
				},
			},
			splitNumber: 4, // 最多显示5条辅助线（4个分割区间）
		},
		title: {
			...chartOpts.title,
			text: ' ',
		},
		grid: { top: 10, right: 10, bottom: 10, left: 10, containLabel: false },
		xAxis: {
			show: false,
			type: 'category',
			boundaryGap: false, // 重要：让面积图从坐标轴开始
		},
	};
};

/**
 * 更新单个图表数据的辅助函数
 * @param {Object} chartData - 响应式图表数据对象
 * @param {Array} newData - 新的数据数组
 */
export const updateSingleChartData = (chartData, newData) => {
	if (chartData && chartData.series && chartData.series[0]) {
		chartData.series[0].data = [...newData];
	}
};

/**
 * 批量更新多个图表数据
 * @param {Object} updates - 更新配置对象 { chartData: newData, ... }
 */
export const batchUpdateChartData = (updates) => {
	Object.entries(updates).forEach(([chartData, newData]) => {
		updateSingleChartData(chartData, newData);
	});
};

/**
 * 获取图表当前数据
 * @param {Object} chartData - 响应式图表数据对象
 * @returns {Array} 当前数据数组
 */
export const getChartCurrentData = (chartData) => {
	return chartData?.series?.[0]?.data || [];
};

// 使用工厂函数创建图表数据实例
export const loadChartData = createChartData('load');
export const cpuChartData = createChartData('cpu');
export const memoryChartData = createChartData('memory');
export const diskChartData = createChartData('disk');
export const newNetworkChartData = createChartData('network', [
	[30, 10, 0, 20, 50, 60],
	[10, 20, 30, 10, 40, 10],
]);

// ==================== 页面数据配置 ====================
// 基础功能列表
export const basicFunctionList = ref([
	{
		label: '网站',
		image: '/static/index/web_site.png',
	},
	{
		label: '数据库',
		image: '/static/index/database.png',
	},
	{
		label: '系统防火墙',
		image: '/static/index/firewall.png',
	},
	{
		label: '终端',
		image: '/static/index/terminal.png',
	},
	{
		label: '安全风险',
		image: '/static/index/security_risk.png',
	},
	{
		label: '监控',
		image: '/static/index/system_monitor.png',
	},
	{
		label: 'SSH管理',
		image: '/static/index/ssh_manager.png',
	},
	{
		label: '文件管理',
		image: '/static/index/file_manager.png',
	},
	{
		label: '计划任务',
		image: '/static/index/cron_task.png',
	},
	{
		label: '日志',
		image: '/static/index/log_manager.png',
	},
]);

// 插件功能列表
export const pluginFunctionList = ref([
	{
		label: '站点监控',
		image: '/static/index/web_monitor.png',
	},
	{
		label: '系统加固',
		image: '/static/index/os_hardening.png',
	},
	{
		label: 'nginx防火墙',
		image: '/static/index/nginx_waf.png',
	},
	{
		label: '防篡改',
		image: '/static/index/tamper_protection.png',
	},
]);

// 环境功能列表
export const environmentFunctionList = ref([
	{
		label: 'nginx',
		image: '/static/index/nginx.png',
		imageStyle: 'width: 65rpx; height: 65rpx;',
	},
	{
		label: 'mysql',
		image: '/static/index/mysql.png',
		imageStyle: 'width: 60rpx; height: 60rpx;',
	},
	{
		label: 'redis',
		image: '/static/index/redis.png',
		imageStyle: 'width: 60rpx; height: 60rpx;',
	},
]);

// ==================== 背景图片和滚动效果 ====================
// 背景图片
export const backgroundImage = ref(bgLight);
export const backgroundImageStyle = ref({
	backgroundImage: `url(${backgroundImage.value})`,
	backgroundSize: 'cover',
	backgroundPosition: 'center',
});

// 背景图片缩放相关变量
export const serverBgScale = ref(1); // 初始缩放比例为1
export const serverBgTranslateY = ref(0); // 垂直偏移量
const maxScrollDistance = 200; // 最大滚动距离，超过此距离缩放效果不再变化
const minScale = 0.6; // 最小缩放比例
const maxTranslateY = -30; // 最大向上偏移量（负值表示向上）

// 计算背景图片的动态样式
export const serverBgStyle = computed(() => {
	return {
		transform: `scale(${serverBgScale.value}) translateY(${serverBgTranslateY.value}px)`,
		transition: 'transform 0.1s ease-out', // 添加平滑过渡效果
	};
});

// 页面滚动事件处理
export const onPageScrollHandler = (e) => {
	const scrollTop = e.detail.scrollTop;

	// 计算滚动比例
	const scrollRatio = Math.min(scrollTop / maxScrollDistance, 1);

	// 计算缩放比例：滚动距离越大，缩放比例越小
	const newScale = 1 - (1 - minScale) * scrollRatio;
	serverBgScale.value = Math.max(newScale, minScale);

	// 计算垂直偏移：滚动距离越大，向上偏移越多
	const newTranslateY = maxTranslateY * scrollRatio;
	serverBgTranslateY.value = newTranslateY;
};

// ==================== 使用示例和文档 ====================
/*
双面积图功能使用指南：

1. 基本使用（向后兼容）：
   // 单系列图表（现有方式不变）
   const cpuChart = createChartData('cpu');
   const memoryChart = createChartData('memory');

2. 双面积图创建：
   // 方式1: 直接使用 createChartData
   const networkChart = createChartData('network');
   const diskIOChart = createChartData('diskio');

   // 方式2: 使用便捷方法
   const networkChart2 = createNetworkDoubleAreaChart();
   const diskIOChart2 = createDiskIODoubleAreaChart();

   // 方式3: 自定义双面积图
   const customChart = createDoubleAreaChart('custom', {
     seriesNames: ['系列A', '系列B'],
     data: [[1, 2, 3, 4], [5, 6, 7, 8]],
     colors: ['#ff6b6b', '#4ecdc4']
   });

3. 高级配置：
   // 带自定义配置的双面积图
   const advancedChart = createChartData('network', null, {
     // ECharts 自定义配置
     grid: { top: 20, bottom: 20 }
   }, {
     seriesNames: ['上行流量', '下行流量'],
     isDoubleArea: true
   });

4. 数据更新：
   // 单系列更新
   updateSingleChartData(cpuChart, [10, 20, 30, 40]);

   // 双系列更新
   updateDoubleAreaChartData(networkChart, [
     [10, 20, 30], // 第一个系列
     [40, 50, 60]  // 第二个系列
   ]);

5. 智能检测：
   // 传入二维数组自动创建双系列
   const autoChart = createChartData('custom', [[1,2,3], [4,5,6]]);

   // 指定类型自动创建双系列
   const networkAuto = createChartData('network'); // 自动双系列

6. 颜色配置：
   支持的预设类型和颜色：
   - network: 橙色(#ff8c00) + 蓝色(#1e90ff)
   - diskio: 粉色(#FF4683) + 青色(#6CC0CF)
   - custom: 绿色(#20a50a) + 黄色(#ffc107)

注意事项：
- 保持向后兼容：现有单系列图表代码无需修改
- 双系列数据格式：[[series1Data], [series2Data]]
- 自动检测：network/diskio 类型自动创建双系列
- 响应式：所有图表数据都是响应式的，支持 Vue 3 响应式更新
*/
