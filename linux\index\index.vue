<template>
	<page-container ref="pageContainer" :isShowNav="false" bgColorPage="#FAFAFA" @scroll="onPageScrollHandler">
		<image
			:src="backgroundImage"
			:style="backgroundImageStyle"
			mode="aspectFill"
			class="w-100% h-360 fixed top-0 z-10"
		/>
		<custom-nav bg-color="unset" title="" :is-back="true"> </custom-nav>
		<view class="content-wrapper px-24">
			<view class="server-info sticky top-200 z-10">
				<view class="relative flex justify-between">
					<view class="flex flex-col">
						<view class="text-36 font-bold">{{ serverDisplayInfo.name }}</view>
						<view class="text-24 py-16"
							>IP：{{ serverDisplayInfo.ip }} | {{ serverDisplayInfo.system }}</view
						>
						<view class="text-24 flex items-center">
							<view class="w-16 h-16 rd-50%" :style="{ backgroundColor: serverStatusColor }"></view>
							<text class="text-24 pl-16" :style="{ color: serverStatusColor }">{{ serverDisplayInfo.uptime }}</text>
						</view>
					</view>
					<image
						src="@/static/index/server-bg.png"
						mode="aspectFit"
						class="absolute -top-130 -right-80 w-460 h-460"
						:style="serverBgStyle"
					></image>
				</view>
			</view>
			<view class="detail mt-68 pb-48 z-1">
				<function-list
					title="功能"
					:function-list="basicFunctionList"
					:show-edit="true"
					:columns="5"
					@itemClick="handleBasicFunctionClick"
					@editClick="() => handleFunctionListEdit('basic')"
				/>
				<function-list
					class="mt-24"
					title="插件"
					:function-list="pluginFunctionList"
					:show-edit="true"
					:columns="4"
					@itemClick="handlePluginFunctionClick"
					@editClick="() => handleFunctionListEdit('plugin')"
				/>
				<function-list
					class="my-24"
					title="环境"
					:function-list="environmentFunctionList"
					:show-edit="true"
					:columns="4"
					@itemClick="handleEnvironmentFunctionClick"
					@editClick="() => handleFunctionListEdit('environment')"
				/>
				<statusInfo title="负载" expanded-text="收起" collapsed-text="详情">
					<template #basic>
						<view class="flex items-center justify-between">
							<view class="flex items-center justify-between w-60%">
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">1分钟</text>
									<text class="text-24 font-800" :style="{ color: loadData.statusColor }">{{ formatLoadValue(loadData.one) }}</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">5分钟</text>
									<text class="text-24 font-800" :style="{ color: loadData.statusColor }">{{ formatLoadValue(loadData.five) }}</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">15分钟</text>
									<text class="text-24 font-800" :style="{ color: loadData.statusColor }">{{ formatLoadValue(loadData.fifteen) }}</text>
								</view>
							</view>
							<view class="">
								<MetricProgressBar
									mode="vertical"
									:value="loadData.percentage"
									height="184rpx"
									thickness="100rpx"
									:server-status="serverDisplayInfo.isOnline"
									:animated="true"
								/>
							</view>
						</view>
					</template>

					<template #details>
						<view class="details-info">
							<ECharts
								canvas-id="load-chart-info"
								chart-type="line"
								:chart-data="loadChartData"
								:opts="getLoadChartStyle()"
								:height="500"
							/>
						</view>
					</template>
				</statusInfo>
				<statusInfo class="mt-24" title="CPU" expanded-text="收起" collapsed-text="详情">
					<template #basic>
						<view class="flex items-center justify-between">
							<view class="flex items-center justify-between w-60%">
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">核心</text>
									<text class="text-24 font-800">4</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">已用</text>
									<text class="text-24 font-800">0.54%</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">空闲</text>
									<text class="text-24 font-800">99%</text>
								</view>
							</view>
							<view class="">
								<ECharts
									canvas-id="cpu-chart"
									chart-type="gauge"
									:chart-data="getBaseChartConfig(chartMap['cpu']?.val, 'CPU')"
									:height="140"
								/>
							</view>
						</view>
					</template>

					<template #details>
						<view class="details-info">
							<view class="flex flex-col mb-24 bg-#F7F7F7 p-24 rd-16">
								<text class="text-28 font-800">基础信息</text>
								<text class="text-24 text-secondary py-8"
									>Intel(R) Xeon(R) Platinum 8361HC CPU @ 2.60GHz</text
								>
								<text class="text-24 text-secondary">1个物理CPU，4个物理核心，4个逻辑核心</text>
							</view>
							<ECharts
								canvas-id="cpu-chart-info"
								chart-type="line"
								:chart-data="cpuChartData"
								:opts="getLoadChartStyle()"
								:height="500"
							/>
						</view>
					</template>
				</statusInfo>
				<statusInfo class="mt-24" title="内存" expanded-text="收起" collapsed-text="详情">
					<template #basic>
						<view class="flex items-center justify-between">
							<view class="flex items-center justify-between w-60%">
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">总计</text>
									<text class="text-24 font-800">8GB</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">已用</text>
									<text class="text-24 font-800">4GB</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">空闲</text>
									<text class="text-24 font-800">4GB</text>
								</view>
							</view>
							<view class="">
								<ECharts
									canvas-id="memory-chart"
									chart-type="gauge"
									:chart-data="getBaseChartConfig(chartMap['memory']?.val, '内存')"
									:height="140"
								/>
							</view>
						</view>
					</template>

					<template #details>
						<view class="details-info">
							<ECharts
								canvas-id="memory-chart-info"
								chart-type="line"
								:chart-data="memoryChartData"
								:opts="getLoadChartStyle()"
								:height="500"
							/>
						</view>
					</template>
				</statusInfo>

				<statusInfo class="mt-24" title="磁盘" expanded-text="收起" collapsed-text="详情">
					<template #desc>
						<text class="text-24 text-secondary"> 若有多个磁盘，可左右滑动查看 </text>
					</template>
					<template #basic>
						<swiper class="server-item h-100">
							<swiper-item>
								<view class="flex items-center justify-between">
									<view class="flex items-center justify-between w-60%">
										<view class="flex flex-col items-center">
											<text class="text-24 mb-16 text-secondary">总计</text>
											<text class="text-24 font-800">100GB</text>
										</view>
										<view class="flex flex-col items-center">
											<text class="text-24 mb-16 text-secondary">已用</text>
											<text class="text-24 font-800">90GB</text>
										</view>
										<view class="flex flex-col items-center">
											<text class="text-24 mb-16 text-secondary">空闲</text>
											<text class="text-24 font-800">10GB</text>
										</view>
									</view>
									<view class="w-40% ml-80 mt-24">
										<MetricProgressBar
											class="z-1"
											mode="horizontal"
											:value="90"
											width="100%"
											thickness="16rpx"
											:server-status="true"
											:animated="true"
											text-position="top-right"
										/>
									</view>
								</view>
							</swiper-item>
							<swiper-item>
								<view class="flex items-center justify-between">
									<view class="flex items-center justify-between w-60%">
										<view class="flex flex-col items-center">
											<text class="text-24 mb-16 text-secondary">总计</text>
											<text class="text-24 font-800">50GB</text>
										</view>
										<view class="flex flex-col items-center">
											<text class="text-24 mb-16 text-secondary">已用</text>
											<text class="text-24 font-800">25GB</text>
										</view>
										<view class="flex flex-col items-center">
											<text class="text-24 mb-16 text-secondary">空闲</text>
											<text class="text-24 font-800">25GB</text>
										</view>
									</view>
									<view class="w-40% ml-80 mt-24">
										<MetricProgressBar
											mode="horizontal"
											:value="66"
											width="100%"
											thickness="16rpx"
											:server-status="true"
											:animated="true"
											text-position="top-right"
										/>
									</view>
								</view>
							</swiper-item>
						</swiper>
					</template>

					<template #details>
						<view class="details-info">
							<view class="flex flex-col mb-24 bg-#F7F7F7 p-24 rd-16">
								<text class="text-28 font-800">基础信息</text>
								<view class="flex items-center py-8">
									<text class="text-24 text-secondary flex-1"> 挂载点：/(/) </text>
									<text class="text-24 text-secondary flex-1"> 文件系统: /dev/vda1 </text>
								</view>
								<text class="text-24 text-secondary"> 类型: xfs ，系统占用: 56.21% </text>
							</view>
							<view class="flex flex-col mb-24 bg-#F7F7F7 p-24 rd-16">
								<text class="text-28 font-800">Inode信息</text>
								<view class="flex items-center py-8">
									<text class="text-24 text-secondary flex-1"> 总数：411155 </text>
									<text class="text-24 text-secondary flex-1"> 已用: 5712545 </text>
								</view>
								<view class="flex items-center">
									<text class="text-24 text-secondary flex-1"> 可用: 55544555665 </text>
									<text class="text-24 text-secondary flex-1"> 使用率: 1.82% </text>
								</view>
							</view>
							<ECharts
								canvas-id="disk-chart-info"
								chart-type="line"
								:chart-data="diskChartData"
								:opts="getLoadChartStyle()"
								:height="500"
							/>
						</view>
					</template>
				</statusInfo>

				<statusInfo class="mt-24" title="网络" expanded-text="收起" collapsed-text="详情" defaultExpanded>
					<template #basic>
						<view class="flex items-center justify-between">
							<view class="flex items-center justify-between w-60%">
								<view class="flex items-center">
									<uv-icon name="arrow-upward" color="#3EAF3B" size="18"></uv-icon>
									<text class="text-24 ml-8 font-800 flex-1">100Mbps</text>
								</view>
								<view class="flex items-center">
									<uv-icon name="arrow-downward" color="#EAD928" size="18"></uv-icon>
									<text class="text-24 ml-8 font-800 flex-1">100Mbps</text>
								</view>
							</view>
						</view>
					</template>

					<template #details>
						<view class="details-info">
							<ECharts
								canvas-id="network-chart-info"
								chart-type="line"
								:chart-data="newNetworkChartData"
								:opts="getLoadChartStyle()"
								:height="500"
							/>
						</view>
					</template>
				</statusInfo>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import { onReady, onShow, onHide } from '@dcloudio/uni-app';
	import CustomNav from '@/components/customNav/index.vue';
	import PageContainer from '@/components/PageContainer/index.vue';
	import ECharts from '@/components/ECharts/index.vue';
	import FunctionList from './functionList.vue';
	import StatusInfo from './statusInfo.vue';
	import MetricProgressBar from '@/components/MetricProgressBar/index.vue';
	import {
		getLoadChartStyle,
		loadChartData,
		cpuChartData,
		getBaseChartConfig,
		chartMap,
		memoryChartData,
		diskChartData,
		newNetworkChartData,
		basicFunctionList,
		pluginFunctionList,
		environmentFunctionList,
		backgroundImage,
		backgroundImageStyle,
		serverBgStyle,
		onPageScrollHandler,
		handleBasicFunctionClick,
		handlePluginFunctionClick,
		handleEnvironmentFunctionClick,
		handleFunctionListEdit,
		pageContainer,
		serverDisplayInfo,
		serverStatusColor,
		getNetwork,
		startTimer,
		stopTimer,
		initData,
		loadData,
		formatLoadValue,
	} from './useController';

	// 生命周期钩子
	onReady(() => {
		// 初始化图表数据
		initData();
		getNetwork();
	});

	onShow(() => {
		// 设置定时器
		startTimer();
	});

	onHide(() => {
		// 清除定时器
		stopTimer();
	});
</script>
<style lang="scss" scoped></style>
